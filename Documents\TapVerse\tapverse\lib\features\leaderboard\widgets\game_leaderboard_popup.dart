import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'dart:math' as math;
import '../../../core/theme/neon_theme.dart';
import '../../../core/models/user_model.dart';
import '../../../core/services/game_navigation_service.dart';
import '../providers/leaderboard_providers.dart';
import 'glassmorphism_components.dart';
import 'enhanced_leaderboard_entry.dart';
import 'animated_list_item.dart';
import 'optimized_widget_pool.dart';

/// Popup modal for individual game leaderboards
class GameLeaderboardPopup extends ConsumerStatefulWidget {
  final GameInfo gameInfo;

  const GameLeaderboardPopup({
    super.key,
    required this.gameInfo,
  });

  @override
  ConsumerState<GameLeaderboardPopup> createState() => _GameLeaderboardPopupState();
}

class _GameLeaderboardPopupState extends ConsumerState<GameLeaderboardPopup>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _fadeController;
  late AnimationController _floatController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<double> _floatAnimation;

  @override
  void initState() {
    super.initState();
    
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );
    
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _floatController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeIn,
    ));

    _floatAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _floatController,
      curve: Curves.easeInOut,
    ));

    // Start animations
    _fadeController.forward();
    _slideController.forward();
    _floatController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _slideController.dispose();
    _fadeController.dispose();
    _floatController.dispose();
    super.dispose();
  }

  Future<void> _closePopup() async {
    await _slideController.reverse();
    await _fadeController.reverse();
    if (mounted) {
      Navigator.of(context).pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    final leaderboardData = ref.watch(gameLeaderboardProvider(widget.gameInfo.id));
    
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Material(
        color: Colors.black.withValues(alpha: 0.7),
        child: GestureDetector(
          onTap: _closePopup,
          child: Container(
            width: double.infinity,
            height: double.infinity,
            child: SlideTransition(
              position: _slideAnimation,
              child: Align(
                alignment: Alignment.bottomCenter,
                child: GestureDetector(
                  onTap: () {}, // Prevent closing when tapping inside popup
                  child: Container(
                    height: MediaQuery.of(context).size.height * 0.85,
                    width: double.infinity,
                    margin: const EdgeInsets.only(top: 50),
                    child: GlassmorphismContainer(
                      borderRadius: 20,
                      padding: EdgeInsets.zero,
                      margin: EdgeInsets.zero,
                      glowColor: widget.gameInfo.primaryColor,
                      child: Column(
                        children: [
                          // Header
                          _buildHeader(),
                          
                          // Content
                          Expanded(
                            child: leaderboardData.when(
                              data: (scores) => _buildLeaderboardContent(scores),
                              loading: () => const Center(
                                child: CircularProgressIndicator(),
                              ),
                              error: (error, stack) => Center(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    const Icon(
                                      Icons.error_outline,
                                      size: 64,
                                      color: Colors.red,
                                    ),
                                    const SizedBox(height: 16),
                                    Text(
                                      'Failed to load leaderboard',
                                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                        color: Colors.white,
                                      ),
                                    ),
                                    const SizedBox(height: 8),
                                    Text(
                                      error.toString(),
                                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                        color: Colors.white.withValues(alpha: 0.7),
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            widget.gameInfo.primaryColor.withValues(alpha: 0.3),
            widget.gameInfo.secondaryColor.withValues(alpha: 0.2),
          ],
        ),
      ),
      child: Row(
        children: [
          // Game icon
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: widget.gameInfo.primaryColor,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              widget.gameInfo.icon,
              color: Colors.white,
              size: 24,
            ),
          ),
          
          const SizedBox(width: 12),
          
          // Game name and difficulty
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.gameInfo.name,
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                Row(
                  children: [
                    ...List.generate(5, (index) {
                      return Icon(
                        index < widget.gameInfo.difficulty
                            ? Icons.star
                            : Icons.star_border,
                        color: index < widget.gameInfo.difficulty
                            ? TapVerseColors.neonGold
                            : Colors.grey,
                        size: 16,
                      );
                    }),
                  ],
                ),
              ],
            ),
          ),
          
          // Close button
          GestureDetector(
            onTap: _closePopup,
            child: Container(
              width: 36,
              height: 36,
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(18),
                border: Border.all(
                  color: Colors.white.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: const Icon(
                Icons.close,
                color: Colors.white,
                size: 20,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLeaderboardContent(List<Map<String, dynamic>> scores) {
    if (scores.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.emoji_events_outlined,
              size: 64,
              color: Colors.white.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'No scores yet!',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Be the first to play and set a high score!',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.white.withValues(alpha: 0.7),
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () {
                _closePopup();
                context.push('/game/${widget.gameInfo.id}');
              },
              icon: const Icon(Icons.play_arrow),
              label: const Text('Play Now'),
              style: ElevatedButton.styleFrom(
                backgroundColor: widget.gameInfo.primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
            ),
          ],
        ),
      );
    }

    final topThree = scores.take(3).toList();
    final remaining = scores.skip(3).toList();

    return Column(
      children: [
        // Top 3 players panel - Fixed height, priority display
        if (topThree.isNotEmpty)
          SizedBox(
            height: 300, // Fixed height to prevent overflow
            child: _buildTop3Panel(topThree),
          ),

        // Remaining players list - Takes remaining space and scrolls
        if (remaining.isNotEmpty)
          Expanded(
            child: _buildRemainingPlayersList(remaining),
          ),
      ],
    );
  }

  Widget _buildTop3Panel(List<Map<String, dynamic>> topThree) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // "Top Players" header
          Row(
            children: [
              Icon(
                Icons.emoji_events,
                color: TapVerseColors.neonGold,
                size: 24,
              ),
              const SizedBox(width: 8),
              const Text(
                'Top Players',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Top 3 players layout - Fixed height container
          SizedBox(
            height: 220, // Fixed height for top 3 cards
            child: _buildTop3Layout(topThree),
          ),
        ],
      ),
    );
  }

  Widget _buildTop3Layout(List<Map<String, dynamic>> topThree) {
    if (topThree.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.emoji_events_outlined,
              size: 48,
              color: Colors.white.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 12),
            Text(
              'No champions yet!',
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.7),
                fontSize: 16,
              ),
            ),
          ],
        ),
      );
    }

    // Responsive layout based on screen width
    return LayoutBuilder(
      builder: (context, constraints) {
        if (constraints.maxWidth > 600) {
          // Wide screen: horizontal layout
          return _buildHorizontalTop3(topThree);
        } else {
          // Narrow screen: podium layout
          return _buildPodiumTop3(topThree);
        }
      },
    );
  }

  Widget _buildHorizontalTop3(List<Map<String, dynamic>> topThree) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 2nd place (left)
          if (topThree.length > 1)
            Container(
              width: 280,
              child: _buildTop3PlayerCard(topThree[1], 2, isCenter: false),
            ),

          const SizedBox(width: 8),

          // 1st place (center, larger)
          if (topThree.isNotEmpty)
            Container(
              width: 320,
              child: _buildTop3PlayerCard(topThree[0], 1, isCenter: true),
            ),

          const SizedBox(width: 8),

          // 3rd place (right)
          if (topThree.length > 2)
            Container(
              width: 280,
              child: _buildTop3PlayerCard(topThree[2], 3, isCenter: false),
            ),
        ],
      ),
    );
  }

  Widget _buildPodiumTop3(List<Map<String, dynamic>> topThree) {
    return Column(
      children: [
        // 1st place (top, larger)
        if (topThree.isNotEmpty)
          Container(
            height: 200,
            child: _buildTop3PlayerCard(topThree[0], 1, isCenter: true),
          ),

        const SizedBox(height: 12),

        // 2nd and 3rd place (bottom row)
        Container(
          height: 160,
          child: Row(
            children: [
              if (topThree.length > 1)
                Expanded(
                  child: _buildTop3PlayerCard(topThree[1], 2, isCenter: false),
                ),

              if (topThree.length > 2) ...[
                const SizedBox(width: 12),
                Expanded(
                  child: _buildTop3PlayerCard(topThree[2], 3, isCenter: false),
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTop3PlayerCard(Map<String, dynamic> playerData, int rank, {required bool isCenter}) {
    final playerId = playerData['uid'] ?? playerData['playerId'] ?? 'unknown';
    final displayName = playerData['displayName'] ?? 'Anonymous';
    final score = playerData['score'] ?? 0;
    final isCurrentUser = playerData['isCurrentUser'] ?? false;

    return AnimatedBuilder(
      animation: _floatAnimation,
      builder: (context, child) {
        final floatOffset = math.sin(_floatAnimation.value * 2 * math.pi) * (isCenter ? 8.0 : 4.0);

        return Transform.translate(
          offset: Offset(0, floatOffset),
          child: _buildTop3Card(playerId, displayName, score, rank, isCurrentUser, isCenter),
        );
      },
    );
  }

  Widget _buildTop3Card(String playerId, String displayName, int score, int rank, bool isCurrentUser, bool isCenter) {
    return Consumer(
      builder: (context, ref, child) {
        final userProfile = ref.watch(userProfileProvider(playerId));

        return userProfile.when(
          data: (profile) => _buildTop3CardContent(profile, displayName, score, rank, isCurrentUser, isCenter),
          loading: () => _buildTop3LoadingCard(displayName, score, rank, isCurrentUser, isCenter),
          error: (_, __) => _buildTop3CardContent(null, displayName, score, rank, isCurrentUser, isCenter),
        );
      },
    );
  }

  Widget _buildTop3CardContent(UserModel? profile, String displayName, int score, int rank, bool isCurrentUser, bool isCenter) {
    final cardHeight = isCenter ? 200.0 : 160.0;
    final cardWidth = isCenter ? 320.0 : 280.0;

    return Container(
      height: cardHeight,
      width: cardWidth,
      child: EnhancedLeaderboardEntry(
        rank: rank,
        userId: profile?.uid ?? 'unknown',
        playerName: displayName,
        score: score,
        timestamp: null, // Top 3 cards don't need timestamp
        isCurrentUser: isCurrentUser,
        gameColor: widget.gameInfo.primaryColor,
        showFullCard: true,
      ),
    );
  }

  Widget _buildTop3LoadingCard(String displayName, int score, int rank, bool isCurrentUser, bool isCenter) {
    final cardHeight = isCenter ? 180.0 : 140.0;
    final cardWidth = isCenter ? 300.0 : 260.0;

    return Container(
      height: cardHeight,
      width: cardWidth,
      child: GlassmorphismContainer(
        padding: const EdgeInsets.all(16),
        margin: const EdgeInsets.all(4),
        glowColor: _getRankColor(rank),
        glowIntensity: isCenter ? 0.6 : 0.4,
        child: const Center(
          child: CircularProgressIndicator(),
        ),
      ),
    );
  }

  Color _getRankColor(int rank) {
    switch (rank) {
      case 1:
        return TapVerseColors.neonGold;
      case 2:
        return TapVerseColors.neonSilver;
      case 3:
        return TapVerseColors.neonBronze;
      default:
        return Colors.grey;
    }
  }

  String _getInitials(String name) {
    if (name.isEmpty) return '?';
    final words = name.trim().split(' ');
    if (words.length == 1) {
      return words[0][0].toUpperCase();
    } else {
      return '${words[0][0]}${words[1][0]}'.toUpperCase();
    }
  }

  String _formatScore(int score) {
    if (score >= 1000000) {
      return '${(score / 1000000).toStringAsFixed(1)}M';
    } else if (score >= 1000) {
      return '${(score / 1000).toStringAsFixed(1)}K';
    } else {
      return score.toString();
    }
  }

  Widget _buildRemainingPlayersList(List<Map<String, dynamic>> remaining) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header
          Row(
            children: [
              Icon(
                Icons.list,
                color: Colors.white.withValues(alpha: 0.7),
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Other Players',
                style: TextStyle(
                  color: Colors.white.withValues(alpha: 0.9),
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              Text(
                '${remaining.length} players',
                style: TextStyle(
                  color: Colors.white.withValues(alpha: 0.5),
                  fontSize: 12,
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // Players list with optimized rendering - Flexible height
          Flexible(
            child: OptimizedListView(
              itemCount: remaining.length,
              physics: const BouncingScrollPhysics(),
              shrinkWrap: true, // Allow list to size itself
              itemBuilder: (context, index) {
                final playerData = remaining[index];
                final rank = index + 4; // Starting from 4th place
                final isCurrentUser = playerData['isCurrentUser'] ?? false;

                return AnimatedListItem(
                  index: index,
                  delay: const Duration(milliseconds: 80),
                  duration: const Duration(milliseconds: 500),
                  child: EnhancedLeaderboardEntry(
                    rank: rank,
                    userId: playerData['uid'] ?? playerData['playerId'] ?? 'unknown',
                    playerName: playerData['displayName'] ?? 'Anonymous',
                    score: playerData['score'] ?? 0,
                    timestamp: playerData['timestamp'],
                    isCurrentUser: isCurrentUser,
                    gameColor: widget.gameInfo.primaryColor,
                    showFullCard: false,
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  void _showPlayerDetails(Map<String, dynamic> playerData, int rank) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          constraints: const BoxConstraints(maxWidth: 400),
          child: EnhancedLeaderboardEntry(
            rank: rank,
            userId: playerData['uid'] ?? playerData['playerId'] ?? 'unknown',
            playerName: playerData['displayName'] ?? 'Anonymous',
            score: playerData['score'] ?? 0,
            timestamp: playerData['timestamp'],
            isCurrentUser: playerData['isCurrentUser'] ?? false,
            gameColor: widget.gameInfo.primaryColor,
            showFullCard: true,
          ),
        ),
      ),
    );
  }

  void _challengePlayer(Map<String, dynamic> playerData) {
    // TODO: Implement challenge functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Challenge feature coming soon!'),
        backgroundColor: widget.gameInfo.primaryColor,
      ),
    );
  }


}
